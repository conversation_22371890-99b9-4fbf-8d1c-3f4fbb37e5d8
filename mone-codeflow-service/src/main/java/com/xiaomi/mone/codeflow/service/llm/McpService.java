package com.xiaomi.mone.codeflow.service.llm;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.xiaomi.mone.codeflow.service.config.AppConfig;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.mcp.McpToolProvider;
import dev.langchain4j.mcp.client.DefaultMcpClient;
import dev.langchain4j.mcp.client.McpClient;
import dev.langchain4j.mcp.client.transport.http.HttpMcpTransport;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.service.AiServices;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * MCP (Model Context Protocol) 服务
 * 使用langchain4j原生MCP支持
 */
@Slf4j
@Service
public class McpService {

    private final ChatLanguageModel chatModel;
    private final AppConfig appConfig;

    @NacosValue(value = "${mcp.enabled:true}", autoRefreshed = true)
    private boolean mcpEnabled;

    @NacosValue(value = "${mcp.server.url:http://localhost:3000/mcp/sse}", autoRefreshed = true)
    private String mcpServerUrl;

    @NacosValue(value = "${mcp.timeout:30}", autoRefreshed = true)
    private int mcpTimeout;

    @NacosValue(value = "${mcp.max-tokens:4096}", autoRefreshed = true)
    private int mcpMaxTokens;

    private McpClient mcpClient;
    private Assistant assistant;

    public McpService(ChatLanguageModel chatModel, AppConfig appConfig) {
        this.chatModel = chatModel;
        this.appConfig = appConfig;
    }

    /**
     * AI助手接口
     */
    private interface Assistant {
        String chat(String userMessage);
    }

    /**
     * 初始化MCP客户端和AI助手
     */
    @PostConstruct
    public void initializeMcp() {
        if (!mcpEnabled) {
            log.info("MCP功能已禁用，跳过初始化");
            return;
        }

        try {
            log.info("初始化MCP客户端，服务器地址: {}", mcpServerUrl);

            // 创建MCP客户端
            mcpClient = new DefaultMcpClient.Builder()
                    .clientName("mone-codeflow")
                    .protocolVersion("2024-11-05")
                    .toolExecutionTimeout(Duration.ofSeconds(mcpTimeout))
                    .transport(new HttpMcpTransport.Builder()
                            .sseUrl(mcpServerUrl)
                            .timeout(Duration.ofSeconds(mcpTimeout))
                            .logRequests(true)
                            .logResponses(true)
                            .build())
                    .build();

            // 创建AI助手，集成MCP工具
            ChatLanguageModel modelToUse = mcpMaxTokens > 0
                    ? appConfig.createChatModelWithMaxTokens(mcpMaxTokens)
                    : chatModel;

            assistant = AiServices.builder(Assistant.class)
                    .chatModel(modelToUse)
                    .toolProvider(McpToolProvider.builder()
                            .mcpClients(mcpClient)
                            .build())
                    .build();

            log.info("MCP客户端和AI助手初始化成功");
        } catch (Exception e) {
            log.error("初始化MCP客户端失败", e);
            mcpClient = null;
            assistant = null;
        }
    }

    /**
     * 清理资源
     */
    @PreDestroy
    public void cleanup() {
        if (mcpClient != null) {
            try {
                mcpClient.close();
                log.info("MCP客户端已关闭");
            } catch (Exception e) {
                log.error("关闭MCP客户端时发生错误", e);
            }
        }
    }

    /**
     * 处理MCP问题查询
     *
     * @param question 用户问题
     * @return MCP处理结果
     */
    public String processQuestion(String question) {
        log.info("开始处理MCP问题: {}", question);

        if (!mcpEnabled) {
            log.warn("MCP功能已禁用");
            return "MCP功能当前已禁用，请联系管理员启用。";
        }

        if (assistant == null) {
            log.error("MCP助手未初始化，尝试使用普通模型回答");
            return fallbackToNormalModel(question);
        }

        try {
            // 使用集成了MCP工具的AI助手处理问题
            String result = assistant.chat(question);
            log.info("MCP问题处理成功，响应长度: {}", result.length());
            return result;
        } catch (Exception e) {
            log.error("使用MCP助手处理问题时发生错误，回退到普通模型", e);
            return fallbackToNormalModel(question);
        }
    }

    /**
     * 回退到普通模型处理
     */
    private String fallbackToNormalModel(String question) {
        try {
            log.info("使用普通模型处理问题");

            SystemMessage systemMessage = SystemMessage.from(
                "你是一个智能助手，请根据用户问题提供准确、有用的回答。"
            );
            UserMessage userMessage = UserMessage.from(question);

            ChatLanguageModel modelToUse = mcpMaxTokens > 0
                    ? appConfig.createChatModelWithMaxTokens(mcpMaxTokens)
                    : chatModel;

            Response<AiMessage> response = modelToUse.generate(systemMessage, userMessage);

            if (response != null && response.content() != null) {
                String result = response.content().text();
                log.info("普通模型响应成功，响应长度: {}", result.length());
                return result;
            } else {
                throw new IllegalStateException("模型返回结果为空");
            }
        } catch (Exception e) {
            log.error("普通模型处理失败", e);
            return "处理问题时发生错误: " + e.getMessage();
        }
    }

    /**
     * 检查MCP服务状态
     *
     * @return MCP服务是否可用
     */
    public boolean checkMcpStatus() {
        if (!mcpEnabled) {
            return false;
        }

        if (mcpClient == null) {
            return false;
        }

        try {
            // 检查MCP客户端连接状态
            // 注意：这里可能需要根据实际的langchain4j MCP API调整
            return true; // 如果客户端存在且没有异常，认为是可用的
        } catch (Exception e) {
            log.error("检查MCP服务状态失败", e);
            return false;
        }
    }

    /**
     * 获取MCP配置信息
     *
     * @return MCP配置信息
     */
    public Map<String, Object> getMcpConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", mcpEnabled);
        config.put("serverUrl", mcpServerUrl);
        config.put("timeout", mcpTimeout);
        config.put("maxTokens", mcpMaxTokens);
        config.put("status", checkMcpStatus());
        config.put("clientInitialized", mcpClient != null);
        config.put("assistantInitialized", assistant != null);
        return config;
    }
}
