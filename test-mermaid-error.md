# 测试文档

这是一个测试文档，包含错误的mermaid语法。

## 流程图

```mermaid
graph TD
    A[开始] --> B{判断条件}
    B -->|是| C[执行操作]
    B -->|否| D[结束]
    C --> policyConfig[政策配置模块]: 校验生效日期范围    polic
    // 这里故意写错语法，缺少箭头符号
    E[其他步骤]
```

## 其他内容

这里是其他正常的markdown内容。

- 列表项1
- 列表项2
- 列表项3

**粗体文本**

*斜体文本*

## 正常的代码块

```java
public class Test {
    public static void main(String[] args) {
        System.out.println("Hello World");
    }
}
```

这个代码块应该保留。
