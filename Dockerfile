FROM micr.cloud.mioffice.cn/wangzhidong1/java-image-tool AS tool
FROM openjdk:21-jdk-slim

# LABEL ENV_ID="_env_id" PROJECT_ID="_project_id" APPLICATION="_application" serverEnv="_server_env"

# 安装必要的工具和依赖
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    pandoc \
    nodejs \
    npm \
    && rm -rf /var/lib/apt/lists/*

# 安装mermaid-filter
RUN npm install -g @mermaid-js/mermaid-cli

# 创建work用户和组
RUN groupadd -g 2001 work && useradd -u 2000 -g 2001 work

COPY --from=tool /opt/mione-curl /bin/mione-curl
COPY --from=tool /opt/init.sh /root/init.sh
COPY mone-codeflow-server/target/mone-codeflow-server-1.0.0-SNAPSHOT.jar /opt/app.jar
ENV TZ=Asia/Shanghai
WORKDIR /
USER work
ENTRYPOINT ["bash","-c","bash /root/init.sh >/root/mishell.log 2>&1; cat /root/mishell.log ; exec java $JAVA_OPT $JAVA_OPT_EXT -jar /opt/app.jar"]
