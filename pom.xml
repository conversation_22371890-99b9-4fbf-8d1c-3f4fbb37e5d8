<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.xiaomi.mone</groupId>
    <artifactId>mone-codeflow</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>mone-codeflow-api</module>
        <module>mone-codeflow-common</module>
        <module>mone-codeflow-service</module>
        <module>mone-codeflow-server</module>
    </modules>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.12.RELEASE</version>
    </parent>

    <properties>
        <java.version>8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <!-- 第三方依赖版本管理 -->
        <lombok.version>1.18.30</lombok.version>
        <logback.version>1.2.12</logback.version>
        <snakeyaml.version>1.26</snakeyaml.version>
        <jsoup.version>1.17.2</jsoup.version>
        <freemarker.version>2.3.32</freemarker.version>
        <okhttp.version>4.9.3</okhttp.version>
        <jackson.version>2.13.0</jackson.version>
        <langchain4j.version>0.31.0</langchain4j.version>
        <larksuite.version>2.4.14</larksuite.version>
        <larksuite.project.version>1.0.16</larksuite.project.version>
        <junit.jupiter.version>5.8.1</junit.jupiter.version>
        <!-- 构建插件版本管理 -->
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <spring-boot-maven-plugin.version>2.7.8</spring-boot-maven-plugin.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 内部依赖 -->
            <dependency>
                <groupId>com.xiaomi.mone</groupId>
                <artifactId>mone-codeflow-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.mone</groupId>
                <artifactId>mone-codeflow-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.mone</groupId>
                <artifactId>mone-codeflow-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.mone</groupId>
                <artifactId>mone-codeflow-server</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 小米内部依赖 -->
            <dependency>
                <groupId>com.xiaomi.youpin</groupId>
                <artifactId>youpin-infra-rpc</artifactId>
                <version>1.28-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.youpin</groupId>
                <artifactId>aop</artifactId>
                <version>1.4-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.youpin</groupId>
                <artifactId>common</artifactId>
                <version>1.7-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi</groupId>
                <artifactId>keycenter-agent-client</artifactId>
                <version>3.6.2</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.mone</groupId>
                <artifactId>mi-tpclogin-sdk</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>run.mone.privateknowledge</groupId>
                <artifactId>private-knowledge-api</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>

            <!-- 日志相关 -->
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <!-- Dubbo相关 -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo</artifactId>
                <version>2.7.12-mone-v20-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.mone</groupId>
                <artifactId>dubbo-auth</artifactId>
                <version>2.7.12-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.xiaomi.mone</groupId>
                <artifactId>dubbo-trace</artifactId>
                <version>2.7.12-SNAPSHOT</version>
            </dependency>

            <!-- Nacos相关 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>dubbo-registry-nacos</artifactId>
                <version>1.2.1-mone-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.spring</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>1.0.10</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-spring-context</artifactId>
                <version>0.3.6-mone-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>1.2.1-mone-v7-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-api</artifactId>
                <version>1.2.1-mone-v3-SNAPSHOT</version>
            </dependency>

            <!-- 工具类依赖 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!-- 第三方API依赖 -->
            <dependency>
                <groupId>dev.langchain4j</groupId>
                <artifactId>langchain4j-open-ai-spring-boot-starter</artifactId>
                <version>${langchain4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.larksuite.oapi</groupId>
                <artifactId>oapi-sdk</artifactId>
                <version>${larksuite.version}</version>
            </dependency>
            <dependency>
                <groupId>com.larksuite.project</groupId>
                <artifactId>oapi-sdk</artifactId>
                <version>${larksuite.project.version}</version>
            </dependency>

            <!-- 测试相关 -->
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${junit.jupiter.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot-maven-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-release-virtual</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-snapshot-virtual</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual</url>
        </snapshotRepository>
    </distributionManagement>

</project>
