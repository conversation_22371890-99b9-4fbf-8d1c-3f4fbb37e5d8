package com.xiaomi.mone.codeflow.server;

import com.google.gson.Gson;
import com.lark.oapi.core.utils.Jsons;
import com.xiaomi.mone.codeflow.api.dto.FeiShuDocumentResDto;
import com.xiaomi.mone.codeflow.api.enums.ReqContentTypeEnum;
import com.xiaomi.mone.codeflow.common.enums.FileTypeEnum;
import com.xiaomi.mone.codeflow.server.provider.FeiShuDocumentProviderImpl;
import com.xiaomi.mone.codeflow.service.feishu.FeiShuUploadFileService;
import com.xiaomi.mone.codeflow.service.feishu.entitys.DocumentEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;

@SpringBootTest(classes = MoneCodeflowBootstrap.class)
public class MoneCodeflowBootstrapTest {
    @Autowired
    FeiShuDocumentProviderImpl feiShuDocumentProvider;
    @Autowired
    FeiShuUploadFileService feiShuUploadFileService;
    @Test
    public void test() throws Exception {
        FeiShuDocumentResDto feiShuDocumentResDto = feiShuDocumentProvider.createDocument("testMD22", "# 标题", ReqContentTypeEnum.MD);
        System.out.println("调用结果："+new Gson().toJson(feiShuDocumentResDto));
    }

    @Test
    public void testUploadFile() throws Exception {
        DocumentEntity documentEntity = new DocumentEntity();
        documentEntity.setFileType(FileTypeEnum.DOCX);
        feiShuUploadFileService.uploadFile(documentEntity,new File("/Users/<USER>/25207914339355510081413396.docx"));
    }

    @Test
    public void testReadDocumentContent() throws Exception {
        FeiShuDocumentResDto feiShuDocumentResDto = feiShuDocumentProvider.readDocumentContent("doxk4wwgPW3EWTkoohBXNUGKWRb");
    }
}
