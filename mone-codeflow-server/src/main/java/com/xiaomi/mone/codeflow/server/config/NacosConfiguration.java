package com.xiaomi.mone.codeflow.server.config;

import org.springframework.context.annotation.Configuration;

@Configuration
public class NacosConfiguration {

    // 暂时注释掉NacosNaming Bean，因为依赖的dubbo-server-registry被注释掉了
    // 如果需要，可以使用标准的Nacos客户端替代

    /*
    @Value("${dubbo.registry.address}")
    private String nacosAddress;

    @Bean
    public NacosNaming nacosNaming() {
        NacosNaming nacosNaming = new NacosNaming();
        String address = nacosAddress.split("//")[1];
        nacosNaming.setServerAddr(address);
        nacosNaming.init();
        return nacosNaming;
    }
    */
}