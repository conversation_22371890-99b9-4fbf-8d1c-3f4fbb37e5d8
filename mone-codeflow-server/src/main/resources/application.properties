#server
init.app.name=@app.name@
init.group=staging

app.name=@app.name@
server.type=@server.type@
server.port=@server.port@
server.debug=@server.debug@

dubbo.group=@dubbo.group@
nacos.config.addrs=@nacos.config.addrs@

log.path=@log.path@

ref.ai.z.service.group=@ref.ai.z.service.group@
ref.ai.z.service.version=@ref.ai.z.service.version@

spring.autoconfigure.exclude=com.mi.xms.sdk.NeptuneAutoConfiguration

# 允许Bean定义覆盖
spring.main.allow-bean-definition-overriding=true

# Nacos配置
nacos.config.server-addr=@nacos.config.addrs@
nacos.config.namespace=
nacos.config.group=DEFAULT_GROUP
nacos.config.data-ids=mone-codeflow,mone-codeflow-feishu,mone-codeflow-feishu-project,mone-codeflow-llm,mone-codeflow-pandoc,mone-codeflow-zeus,core-terms-definition.json
nacos.config.auto-refresh=true

# Dubbo配置
dubbo.application.name=@app.name@
dubbo.application.qos-enable=false
dubbo.protocol.name=dubbo
dubbo.protocol.port=@dubbo.protocol.port@
dubbo.protocol.transporter=netty4
dubbo.protocol.threadpool=fixed
dubbo.protocol.threads=200
dubbo.registry.address=@dubbo.registry.address@
dubbo.consumer.timeout=30000
dubbo.provider.timeout=30000