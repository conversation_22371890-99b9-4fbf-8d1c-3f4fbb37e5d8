#server
app.name=mone-codeflow
server.type=staging
server.port=8080
server.debug=true

dubbo.group=staging
dubbo.protocol.port=-1
dubbo.registry.address=nacos://nacos.test.b2c.srv:80
nacos.config.addrs=nacos.test.b2c.srv:80

log.path=/tmp/log

ref.ai.z.service.group=staging-new
ref.ai.z.service.version=1.0

# MCP配置
mcp.enabled=true
mcp.server.url=http://localhost:3000
mcp.timeout=30
mcp.max-tokens=4096